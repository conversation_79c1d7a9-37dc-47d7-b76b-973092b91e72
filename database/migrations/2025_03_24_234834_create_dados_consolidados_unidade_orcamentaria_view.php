<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        
        
        DB::statement("CREATE MATERIALIZED VIEW dados_consolidados.unidade_orcamentaria AS
SELECT 
    '2022' AS exercicio,
    uo.id,
    uo.codigo,
    uo.descricao,
    NULL AS tipo_ato,
    NULL AS numero_ato,
    NULL AS data_publicacao_ato,
    NULL AS veiculo_publicacao_ato,
    uo.created_at,
    uo.updated_at,
    rp.remessa_id
FROM exercicio_2022.sicap_unidade_orcamentaria uo
JOIN public.remessa_parcials rp ON uo.remessa_parcial_id = rp.id


UNION ALL

SELECT 
    '2024' AS exercicio,
    uo.id,
    uo.codigo,
    uo.descricao,
    uo.tipo_ato,
    uo.numero_ato,
    uo.data_publicacao_ato,
    uo.veiculo_publicacao_ato,
    uo.created_at,
    uo.updated_at,
    rp.remessa_id
FROM exercicio_2024.sicap_unidade_orcamentaria uo
JOIN public.remessa_parcials rp ON uo.remessa_parcial_id = rp.id

UNION ALL

SELECT 
    '2025' AS exercicio,
    uo.id,
    uo.codigo,
    uo.descricao,
    uo.tipo_ato,
    uo.numero_ato,
    uo.data_publicacao_ato,
    uo.veiculo_publicacao_ato,
    uo.created_at,
    uo.updated_at,
    rp.remessa_id
FROM exercicio_2025.sicap_unidade_orcamentaria uo
JOIN public.remessa_parcials rp ON uo.remessa_parcial_id = rp.id;");



    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('dados_consolidados.unidade_orcamentaria');
    }
};
