APP_NAME="SIAP - Sistema Integrado de Auditoria Pública"
APP_ENV=local
APP_KEY=base64:zPhNDq9vmM7jJIKMgJfLAh4jALc/wBAQ+LT2NiRL4IE=
APP_DEBUG=true
APP_URL=http://localhost:8084
APP_SERVICE='laravel-auditoria-v4'
WWWGROUP=1000
WWWUSER=1000

DEBUGBAR_ENABLED=false

BROADCAST_DRIVER=reverb
CACHE_DRIVER=file
FILESYSTEM_DISK=local
QUEUE_CONNECTION=database
SESSION_DRIVER=file
SESSION_LIFETIME=120

CERTIFICATE_PATH=certificate
CERTIFICATE_NAME="Tribunal de Contas do Estado de Alagoas"
CERTIFICATE_LOCATION=www.tceal.tc.br
CERTIFICATE_REASON=""
CERTIFICATE_CONTACT=https://www.tceal.tc.br/view/portal-transparencia/diario-oficial.php

DB_CONNECTION=pgsql
DB_HOST=postgres-auditoria
DB_PORT=5432
DB_DATABASE=auditoria
DB_USERNAME=postgres
DB_PASSWORD=postgres
DB_SCHEMAS=comunicacao,crawler,relatorio,sapc,tabelas_auxiliares,exercicio_2021,exercicio_2022,exercicio_2024,exercicio_2025

ID_CONTEXTO_SEGURANCA_LACUNA=12a0b5db-1ee7-43e5-a7c3-75d7e056d563

LOG_CHANNEL=stack
LOG_LEVEL=debug

MAIL_NOTIFICATIONS_ENABLED=false
MAIL_ADDRESS_FALLBACK=null
MAIL_MAILER=smtp
MAIL_HOST=smtp.tceal.tc.br
MAIL_PORT=587
MAIL_ENCRYPTION=tls
MAIL_USERNAME="<EMAIL>"
MAIL_PASSWORD="gQP8zb0kTNck0r0E"
MAIL_FROM_ADDRESS="<EMAIL>"
MAIL_FROM_NAME="${APP_NAME}"

VITE_BASE_URL=""
VITE_CLIENT_NAME="Tribunal de Contas de Alagoas"
VITE_PUSHER_APP_KEY="${PUSHER_APP_KEY}"
VITE_PUSHER_APP_CLUSTER="${PUSHER_APP_CLUSTER}"

# Metronic vars
VITE_APP_NAME="SIAP"
VITE_APP_VERSION="8.2.0"
VITE_APP_DEMO="demo8"
VITE_APP_FULL_NAME="SIAP - Sistema Integrado de Auditoria Pública"
VITE_APP_API_URL="/api"

# Definicao do ambiente para mensagem de homolog nos portais, ex: "prod", "homolog"
VITE_APP_ENVIRONMENT="prod"

# Configuração do Lacuna
# Arquivo que contém chave no repositório: https://github.com/LacunaSoftware/PkiSuiteSamples/blob/master/php/plain/config.php
LACUNA_ENDPOINT=https://pki.rest/
LACUNA_ACCESS_TOKEN="up4TIS8gGNax0AKJwsS0MOnTsuzhkkSYY1gxdSB_ZBZlkEn453qRERfaOxtnhEDsceuu3I-gLcz_ZAAp3YKbmlFHXesbmIoDJe3P9HU0Fc_feUNw_OAfDVk7-rgf4LrweS3bOr-Nm3-ay82sGin5J9n5I49UWVeLk41BJht17EXlDgAZK_DGrGmoel2WGrusOImtn2S2TeeupyT5vYOpZEVSqI2oCXzSnzJGZXk8xyauPGu7kA0EJ_LSFXB44lM9B0SsAe88YnyRPJRaohZYvwO0hma9Fw03jmU5063Lg9o7j7YO9118PxOhE8nF3ae5hlWsGGcvyqGGt71KyqPK8PR_8noyBbcWFnERoFADbI3q-CV9FtcDtUM2JOURHk7bA7jgLREm1VxLpH-fiGFS-fZVlrnhuCwDhQsqXME7IkdTAzcWyAd1F_R0GCMqiih7oA1CovVUQTmPsPsSqiFeeYUPjm1FoIWu2PEpQOxhUORGY3Bw"
LACUNA_WEBPKI_LICENSE=null
LACUNA_TEST=true

# configuracao do Cardug
CARDUG_URL="http://localhost:8000/api"

DISABLE_LAZYLOADING=false

# url do menu do admin
URL_REPORTS=http://hml-bi.tceal.tc.br/Reports/browse/
# url do menu do portal auditor
VITE_APP_URL_REPORTS="${URL_REPORTS}"

NOVA_LICENSE_KEY=sp6qH6RRVbNNCyH8KpmfrT2lsY80U983FJmUGSBWmSU03PRFEL

# Storybookjs
# Permite usar o vuetify no storybook
STORYBOOK_USE_VUETIFY=true

BROADCAST_CONNECTION=reverb

REVERB_SERVER=reverb
REVERB_APP_ID=316275
REVERB_APP_KEY=djho2jpbmpf2prelekfw
REVERB_APP_SECRET=i7duv0pfc3rubjgga0yv
REVERB_HOST="localhost"
REVERB_PORT=8085
REVERB_SCHEME=http

#REVERB_SERVER_HOST=0.0.0.0
REVERB_SERVER_PORT=8085

VITE_REVERB_APP_KEY="${REVERB_APP_KEY}"
VITE_REVERB_HOST="ws-dev-siap.aisolutions.tec.br"
VITE_REVERB_PORT=443
VITE_REVERB_SCHEME=https

VITE_APP_URL=https://dev-siap.aisolutions.tec.br

SLACK_BOT_USER_OAUTH_TOKEN=********************************************************
SLACK_BOT_USER_DEFAULT_CHANNEL=C02AU9VEY6S
SLACK_NOTIFICATIONS_ENABLED=false

API_TOKEN_FALLBACK=**********