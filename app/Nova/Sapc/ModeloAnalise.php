<?php

namespace App\Nova\Sapc;

use App\Enums\Esfera;
use App\Enums\Sapc\TipoAnalise as SapcTipoAnalise;
use App\Nova\Diretoria;
use App\Nova\Filters\EnumFilter;
use App\Nova\Filters\Sapc\Diretoria as SapcDiretoria;
use App\Nova\Resource;
use App\Nova\TipoUnidade;
use Illuminate\Http\Request;
use Laravel\Nova\Fields\BelongsTo;
use Laravel\Nova\Fields\BelongsToMany;
use Laravel\Nova\Fields\Date;
use Laravel\Nova\Fields\HasMany;
use Laravel\Nova\Fields\ID;
use <PERSON>vel\Nova\Fields\Image;
use Laravel\Nova\Fields\Text;
use Laravel\Nova\Fields\Textarea;
use Laravel\Nova\Http\Requests\NovaRequest;
use Outl1ne\NovaDetachedFilters\HasDetachedFilters;
use Outl1ne\NovaDetachedFilters\NovaDetachedFilters;
use SimpleSquid\Nova\Fields\Enum\Enum;

class ModeloAnalise extends Resource
{
    use HasDetachedFilters;

    /**
     * The model the resource corresponds to.
     *
     * @var class-string<\App\Models\Sapc\ModeloAnalise>
     */
    public static $model = \App\Models\Sapc\ModeloAnalise::class;

    /**
     * The single value that should be used to represent the resource when being displayed.
     *
     * @var string
     */
    public static $title = 'nome';

    public static $group = 'e-Contas';

    /**
     * The columns that should be searched.
     *
     * @var array
     */
    public static $search = [
        'id',
        'nome',
        'data_inicio',
        'data_fim',
        'tipo_analise',
        'esfera',
    ];

    public static function uriKey()
    {
        return 'modelos-analises';
    }

    public static function label()
    {
        return 'Modelos de Análises';
    }

    public static function singularLabel()
    {
        return 'Modelo de Análise';
    }

    /**
     * Get the fields displayed by the resource.
     *
     * @return array
     */
    public function fields(NovaRequest $request)
    {
        return [
            ID::make()->sortable(),
            Text::make(__('Nome'), 'nome')->required()->sortable(),
            BelongsTo::make(__('Diretoria'), 'diretoria', Diretoria::class)->required()->viewable(false)->sortable(),
            Date::make('Data de Validade Início', 'data_inicio')->required()->sortable(),
            Date::make('Data de Validade Fim', 'data_fim')->sortable(),

            Enum::make('Tipo Análise', 'tipo_analise')->attach(SapcTipoAnalise::class)->displayUsingLabels()->sortable(),
            Enum::make('Esfera', 'esfera')->attach(Esfera::class)->displayUsingLabels()->sortable(),

            TextArea::make('Cabeçalho', 'cabecalho')
                ->alwaysShow()
                ->rules('nullable', 'string', 'max:500'),

            Image::make('Imagem Cabeçalho', 'imagem_cabecalho')
                ->disk('image') // ou outro disco configurado
                ->path('modelos-analises/imagens') // pasta no disco
                ->rules('nullable', 'image', 'max:500'),

            TextArea::make('Rodapé', 'rodape')
                ->alwaysShow()
                ->rules('nullable', 'string', 'max:500'),

            Image::make('Imagem Rodapé', 'imagem_rodape')
                ->disk('image')
                ->path('modelos-analises/imagens')
                ->nullable()
                ->rules('nullable', 'image', 'max:500'),

            BelongsToMany::make('Tipos unidades', 'tiposUnidades', TipoUnidade::class),

            HasMany::make('Formulários', 'modelosAnalisesFormularios', ModeloAnaliseFormulario::class),
            HasMany::make('Análises', 'analises', Analise::class),

        ];
    }

    /**
     * Get the cards available for the request.
     *
     * @return array
     */
    public function cards(NovaRequest $request)
    {
        return [
            (new NovaDetachedFilters([
                (new SapcDiretoria)->withMeta(['width' => 'w-1/3']),
                (new EnumFilter('tipo_analise', 'App\Enums\Sapc\TipoAnalise'))->withMeta(['width' => 'w-1/3']),
                (new EnumFilter('esfera', 'App\Enums\Esfera'))->withMeta(['width' => 'w-1/3']),
            ]))->width('full'),
        ];
    }

    /**
     * Get the filters available for the resource.
     *
     * @return array
     */
    public function filters(NovaRequest $request)
    {
        return [];
    }

    /**
     * Get the lenses available for the resource.
     *
     * @return array
     */
    public function lenses(NovaRequest $request)
    {
        return [];
    }

    /**
     * Get the actions available for the resource.
     *
     * @return array
     */
    public function actions(NovaRequest $request)
    {
        return [];
    }

    public function authorizedToReplicate(Request $request)
    {
        return false;
    }

    public static function redirectAfterCreate(NovaRequest $request, $resource)
    {
        return '/resources/modelos-analises/'.$resource->id;
    }

    public static function redirectAfterUpdate(NovaRequest $request, $resource)
    {
        return '/resources/modelos-analises/'.$resource->id;
    }
}
