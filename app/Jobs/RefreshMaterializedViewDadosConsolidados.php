<?php

namespace App\Jobs;

use App\Traits\DiscoverLoaderClassNameTrait;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

class RefreshMaterializedViewDadosConsolidados
{
    use DiscoverLoaderClassNameTrait;
    use Dispatchable, SerializesModels;

    public $tries = 5;

    /**
     * Executa o job.
     *
     * @return void
     */
    public function handle()
    {
        try {
            DB::statement('REFRESH MATERIALIZED VIEW dados_consolidados.unidade_orcamentaria');
            DB::statement('REFRESH MATERIALIZED VIEW dados_consolidados.credito_adicional');
            Log::info('Materialized view atualizada com sucesso.');
        } catch (\Exception $e) {
            Log::error('Erro ao atualizar materialized view: '.$e->getMessage());
        }
    }
}
