<?php

namespace App\Http\Controllers;

use App\Enums\Esfera;
use App\Enums\Poder;
use App\Enums\Sapc\FormularioHistoricoAcao;
use App\Enums\Sapc\StatusAnaliseFormulario;
use App\Enums\TipoUnidade;
use App\Http\Requests\Sapc\AnaliseFormularioAnexoRequest;
use App\Http\Requests\Sapc\AnaliseFormularioHistoricoAcaoRequest;
use App\Http\Requests\Sapc\AnaliseFormularioRequest;
use App\Http\Resources\RemessaParcialResource;
use App\Http\Resources\Sapc\AnaliseFormularioResource;
use App\Http\Resources\Sapc\AnaliseFormularioVersaoResource;
use App\Http\Resources\Sapc\AnaliseResource;
use App\Models\Sapc\Analise;
use App\Models\Sapc\AnaliseFormulario;
use App\Models\Sapc\AnaliseFormularioAnexo;
use App\Models\Sapc\AnaliseFormularioVersao;
use App\Services\SapcService;
use Exception;
use Illuminate\Database\Eloquent\ModelNotFoundException;
use Illuminate\Http\Request;
use Illuminate\Http\Response;
use Illuminate\Support\Facades\Log;

class SapcController
{
    protected SapcService $sapcService;

    public function __construct(SapcService $sapcService)
    {
        $this->sapcService = $sapcService;
    }

    public function analiseShow(Analise $analise)
    {
        if (! $analise) {
            throw new ModelNotFoundException('Análise não encontrada');
        }

        return new AnaliseResource($analise);
    }

    public function analiseRemessas(Analise $analise)
    {
        if (! $analise) {
            throw new \Illuminate\Database\Eloquent\ModelNotFoundException('Análise não encontrada');
        }

        $parciais = $this->sapcService->getRemessasParciasDaAnalise($analise);

        return RemessaParcialResource::collection($parciais);
    }

    public function analisesFinalizadas(Request $request)
    {
        $analises = Analise::query()
            ->with(['modeloAnalise', 'unidadeGestora', 'diretoria'])
            ->whereIn('status', ['finalizada_intempestiva', 'finalizada_tempestiva'])
            ->paginate($request->input('itemsPerPage', 20));

        return AnaliseResource::collection($analises);
    }

    public function analisesNaoFinalizadas(Request $request)
    {
        $analises = Analise::query()
            ->with(['modeloAnalise', 'unidadeGestora', 'diretoria'])
            ->whereNotIn('status', ['finalizada_intempestiva', 'finalizada_tempestiva'])
            ->paginate($request->input('itemsPerPage', 20));

        return AnaliseResource::collection($analises);
    }

    public function analisesTodas(Request $request)
    {
        $queryBuilder = Analise::query()
            ->with(['modeloAnalise', 'unidadeGestora', 'diretoria']);

        if ($request->input('exercicio')) {
            $queryBuilder->where('exercicio', $request->input('exercicio'));
        }

        if (! is_null($request->input('poder'))) {
            $poder = Poder::fromValue((int) $request->input('poder'));
            $queryBuilder->whereHas('unidadeGestora', function ($query) use ($poder) {
                $query->where('poder', $poder);
            });
        }

        if (! is_null($request->input('esfera'))) {
            $esfera = Esfera::fromValue((int) $request->input('esfera'));
            $queryBuilder->whereHas('unidadeGestora', function ($query) use ($esfera) {
                $query->where('esfera', $esfera);
            });
        }

        if (! is_null($request->input('tipoUG'))) {
            $tipoUG = TipoUnidade::fromValue((int) $request->input('tipoUG'));
            $queryBuilder->whereHas('unidadeGestora', function ($query) use ($tipoUG) {
                $query->where('tipo_unidade', $tipoUG);
            });
        }

        if (! is_null($request->input('municipio'))) {
            $queryBuilder->whereHas('unidadeGestora', function ($query) use ($request) {
                $query->where('cidade_id', $request->input('municipio'));
            });
        }

        if ($request->input('tipoAnalise')) {
            $queryBuilder->whereHas('modeloAnalise', function ($query) use ($request) {
                $query->where('tipo_analise', $request->input('tipoAnalise'));
            });
        }

        if ($request->input('unidadeGestora')) {
            $queryBuilder->where('unidade_gestora_id', $request->input('unidadeGestora'));
        }

        if ($request->input('status')) {
            $queryBuilder->where('status', $request->input('status'));
        }

        $queryBuilder->orderBy('sapc.analise.created_at', 'desc');

        $analises = $queryBuilder->paginate($request->input('itemsPerPage', 20));

        $response = AnaliseResource::collection($analises);

        $response->additional(['can_manager_analise' => ! auth()->user()->isDiretor()]);

        return $response;
    }

    public function analiseFormularios($analise)
    {

        $analisesFormularios = AnaliseFormulario::select('sapc.analise_formulario.*')
            ->with(['analise', 'modeloFormulario', 'user'])
            ->join('sapc.modelo_analise_formulario as maf', 'maf.modelo_formulario_id', '=', 'analise_formulario.modelo_formulario_id')
            ->where('analise_id', $analise)
            ->groupBy('sapc.analise_formulario.id')
            ->orderByRaw('MIN(maf.ordenacao)')
            ->get();

        return AnaliseFormularioResource::collection($analisesFormularios);
    }

    public function formularioStore(AnaliseFormularioRequest $request)
    {
        $data = $request->all();

        $analiseFormulario = AnaliseFormulario::where('id', $data['formularioId'])->first();

        if (($analiseFormulario->texto === $data['texto']) && ($data['status'] != StatusAnaliseFormulario::Finalizado)) {
            abort(400, 'Os textos não podem ser iguais.');
        }

        return $this->sapcService->storeAnalise($analiseFormulario, $data);
    }

    public function formularioShow(Analise $analise, AnaliseFormulario $analiseFormulario)
    {
        if (! $analise) {
            throw new \Illuminate\Database\Eloquent\ModelNotFoundException('Análise não encontrada');
        }

        if (! $analiseFormulario) {
            throw new \Illuminate\Database\Eloquent\ModelNotFoundException('Formulario não encontrada');
        }

        return new AnaliseFormularioResource($analiseFormulario);
    }

    public function formularioGetVersoes(Analise $analise, AnaliseFormulario $analiseFormulario)
    {
        $result = $this->sapcService->listarVersoes($analiseFormulario);

        return response()->json($result, 200);
    }

    public function formularioReabrir(Analise $analise, AnaliseFormulario $analiseFormulario, SapcService $sapcService)
    {
        if (! $analise) {
            throw new \Illuminate\Database\Eloquent\ModelNotFoundException('Análise não encontrada');
        }

        if (! $analiseFormulario) {
            throw new \Illuminate\Database\Eloquent\ModelNotFoundException('Formulario não encontrada');
        }

        if ($analiseFormulario->status !== StatusAnaliseFormulario::Finalizado) {
            throw new \Illuminate\Database\Eloquent\ModelNotFoundException('Formulario não está finalizado');
        }

        $analiseFormulario->status = StatusAnaliseFormulario::Reaberto;
        $analiseFormulario->save();
        $sapcService->logAcao($analiseFormulario, FormularioHistoricoAcao::ReabrirFormulario, $analiseFormulario->versao);

        return new AnaliseFormularioResource($analiseFormulario);
    }

    public function formularioLogAcao(Analise $analise, AnaliseFormulario $analiseFormulario, AnaliseFormularioHistoricoAcaoRequest $request)
    {
        return response()->json(['data' => $this->sapcService->logAcao($analiseFormulario, $request->acao, $request->valor)]);
    }

    public function formularioGetAcoes(Analise $analise, AnaliseFormulario $analiseFormulario)
    {
        return response()->json($this->sapcService->getAcoes($analiseFormulario));
    }

    public function formularioRestoreVersao($analise, AnaliseFormulario $analiseFormulario, AnaliseFormularioVersao $analiseVersaoFormulario)
    {
        $versaoAtual = $this->sapcService->restaurarVersao($analiseVersaoFormulario);
        $this->sapcService->logAcao($analiseFormulario, FormularioHistoricoAcao::RestaurarVersao, $analiseVersaoFormulario->versao);

        return response()->json(['data' => $versaoAtual]);
    }

    public function formularioGetVersao($analise, $analiseFormulario, AnaliseFormularioVersao $analiseVersaoFormulario)
    {
        $analiseVersaoFormulario->load(['usuario', 'formulario']);

        return response()->json(new AnaliseFormularioVersaoResource($analiseVersaoFormulario));
    }

    public function analiseDownload(Analise $analise)
    {
        $data = $this->sapcService->downloadFormulariosAnalise($analise);

        return response()->streamDownload(function () use ($data) {
            readfile($data['pdf_path']);
        }, $data['name'], [
            'Content-Type' => 'application/pdf',
            'Content-Disposition' => 'attachment; filename="'.$data['name'].'"',
        ]);
    }

    public function analiseDownload2(Analise $analise)
    {
        $data = $this->sapcService->downloadFormulariosAnalise($analise);

        return response()->streamDownload(function () use ($data) {
            echo $data->pdf;
        }, $data->name, ['Content-Type' => 'application/pdf']);
    }

    public function anexoIndex(Analise $analise, AnaliseFormulario $analiseFormulario)
    {
        $result = $this->sapcService->listarAnexos($analiseFormulario);

        return response()->json($result, 200);
    }

    public function anexoUpload(AnaliseFormularioAnexoRequest $request, Analise $analise, AnaliseFormulario $analiseFormulario)
    {
        $result = $this->sapcService->uploadAnaliseFormularioAnexo(
            $analiseFormulario,
            $request->file('file'),
            $request->input('descricao')
        );

        return response()->json($result, 200);
    }

    public function anexoDestroy(AnaliseFormularioAnexo $anexo)
    {
        $result = $this->sapcService->excluirAnaliseFormularioAnexo($anexo);

        return response()->json($result, 200);
    }

    public function anexoDownload($analise, $analiseFormulario, AnaliseFormularioAnexo $anexo)
    {
        $uploadPath = storage_path('app/analises/formularios/anexos/');

        return response()->download($uploadPath.$anexo->arquivo);
    }

    public function autoSaveAnalise($analise, $analiseFormulario, Request $request)
    {
        try {
            $this->sapcService
                ->autoSaveAnalise($analise, $analiseFormulario, $request);

            return response()->json([
                'success' => 'Análise salva Automáticamente!',
            ]);
        } catch (Exception $e) {
            Log::error("Falha no salvamento automático da análise: {$e->getMessage()}");

            return response()->json(
                ['error' => $e->getMessage()],
                Response::HTTP_INTERNAL_SERVER_ERROR
            );
        }
    }

    public function updateProtocoloEtce($analise, Request $request)
    {
        $request->validate([
            'protocolo_etce' => 'string|max:16',
        ]);

        $this->sapcService
            ->updateProtocoloEtce(
                $analise,
                $request->input('protocolo_etce')
            );

        return response()->json(['message' => 'Protocolo eTCE atualizado com sucesso.']);
    }

    /**
     * Atualiza os marcadores de referência em todos os formulários da análise
     */
    public function atualizarMarcadores(Analise $analise)
    {
        try {
            $referencias = $this->sapcService->extrairReferencias($analise);

            return response()->json([
                'message' => 'Marcadores atualizados com sucesso.',
                'referencias_encontradas' => count($referencias),
                'referencias' => $referencias
            ]);
        } catch (Exception $e) {
            Log::error("Erro ao atualizar marcadores: {$e->getMessage()}");

            return response()->json(
                ['error' => 'Erro ao atualizar marcadores.'],
                Response::HTTP_INTERNAL_SERVER_ERROR
            );
        }
    }
}
