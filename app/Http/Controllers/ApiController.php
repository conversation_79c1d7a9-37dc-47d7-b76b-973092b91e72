<?php

namespace App\Http\Controllers;

use App\Http\Requests\ApiBase\MunicipioRequest;
use App\Http\Resources\ApiBase\ComportamentoResource;
use App\Http\Resources\ApiBase\DiretoriaResource;
use App\Http\Resources\ApiBase\EsferaResource;
use App\Http\Resources\ApiBase\ExercicioResource;
use App\Http\Resources\ApiBase\GrupoRegionalResource;
use App\Http\Resources\ApiBase\MunicipioResource;
use App\Http\Resources\ApiBase\PoderResource;
use App\Http\Resources\ApiBase\QuestionarioStatusResource;
use App\Http\Resources\ApiBase\QuestionarioTipoResource;
use App\Http\Resources\ApiBase\RelatoriaResource;
use App\Http\Resources\ApiBase\SubtipoUnidadeResource;
use App\Http\Resources\ApiBase\TipoAdministracaoResource;
use App\Http\Resources\ApiBase\TipoUnidadeResource;
use App\Http\Resources\PeriodoRemessaResource;
use App\Http\Resources\TipoLayoutResource;
use App\Http\Resources\UnidadeGestoraResource;
use App\Models\PeriodoRemessa;
use App\Services\ApiService;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\AnonymousResourceCollection;
use Illuminate\Http\Resources\Json\JsonResource;

class ApiController extends Controller
{
    public function __construct(protected ApiService $apiService) {}

    public function listPeriodoRemessaByExercise($exercise)
    {
        $periodoRemessa = PeriodoRemessa::select('id', 'nome', 'bimestre')
            ->orderBy('bimestre', 'ASC')
            ->groupBy(['nome',  'id', 'bimestre'])
            ->wherePermanente(false)
            ->whereExercicio($exercise)
            ->get()
            ->map(fn ($item) => [
                'id' => $item->id,
                'nome' => $item->nome,
            ]);

        return response()->json($periodoRemessa);
    }

    public function fetchExercicios(): AnonymousResourceCollection
    {
        return ExercicioResource::collection($this->apiService->fetchExercicios());
    }

    public function fetchUnidadesGestoras(Request $request, ?string $searchTerm = null)
    {
        $unidades = $this->apiService->fetchUnidadesGestoras($request);

        return UnidadeGestoraResource::collection($unidades ?? []);
    }

    public function fetchAllUnidadesGestoras(Request $request): AnonymousResourceCollection
    {
        $unidades = $this->apiService->fetchAllUnidadesGestoras($request);

        return UnidadeGestoraResource::collection($unidades);
    }

    public function fetchTiposUnidade(Request $request): AnonymousResourceCollection
    {
        return TipoUnidadeResource::collection($this->apiService->fetchTiposUnidade());
    }

    public function fetchMunicipios(MunicipioRequest $request): AnonymousResourceCollection
    {
        return MunicipioResource::collection($this->apiService->fetchMunicipio($request->validated()));
    }

    public function fetchRelatorias(Request $request): AnonymousResourceCollection
    {
        return RelatoriaResource::collection($this->apiService->fetchRelatorias());
    }

    public function fetchEsferas(Request $request): AnonymousResourceCollection
    {
        return EsferaResource::collection($this->apiService->fetchEsferas());
    }

    public function fetchPoderes(Request $request)
    {
        return PoderResource::collection($this->apiService->fetchPoderes());
    }

    public function fetchQuestionarioStatus(Request $request)
    {
        return QuestionarioStatusResource::collection($this->apiService->fetchQuestionarioStatus());
    }

    public function fetchQuestionarioTipos(Request $request): AnonymousResourceCollection
    {
        return QuestionarioTipoResource::collection($this->apiService->fetchQuestionarioTipos());
    }

    public function fetchGruposRegionais(Request $request)
    {
        return GrupoRegionalResource::collection($this->apiService->fetchGruposRegionais());
    }

    public function fetchTiposAdministracao(Request $request)
    {
        return TipoAdministracaoResource::collection($this->apiService->fetchTiposAdministracao());
    }

    public function fetchSubtiposUnidades(Request $request)
    {
        return SubtipoUnidadeResource::collection($this->apiService->fetchSubtiposUnidades());
    }

    public function fetchDiretorias(Request $request)
    {
        return DiretoriaResource::collection($this->apiService->fetchDiretorias());
    }

    public function fetchComportamentos(Request $request)
    {
        return ComportamentoResource::collection($this->apiService->fetchComportamentos());
    }

    public function fetchPeriodosRemessa(Request $request): AnonymousResourceCollection
    {
        $periodoRemessas = $this->apiService->fetchPeriodosRemessa($request);

        return PeriodoRemessaResource::collection($periodoRemessas);
    }

    public function fetchTiposLayouts(Request $request): AnonymousResourceCollection
    {
        return TipoLayoutResource::collection($this->apiService->fetchTiposLayouts($request));
    }

    public function fetchNaturezasLancamento(Request $request): AnonymousResourceCollection
    {
        return JsonResource::collection($this->apiService->fetchNaturezasLancamento($request));
    }

    public function fetchPeriodoRemessaStatus()
    {
        return JsonResource::collection($this->apiService->fetchPeriodoRemessaStatus());
    }

    public function fetchQuestionarioExercicios(): AnonymousResourceCollection
    {
        return JsonResource::collection($this->apiService->fetchQuestionarioExercicios());
    }
}
