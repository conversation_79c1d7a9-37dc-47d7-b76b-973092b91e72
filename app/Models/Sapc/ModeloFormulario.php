<?php

namespace App\Models\Sapc;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class ModeloFormulario extends Model
{
    use HasFactory;

    protected $table = 'sapc.modelo_formulario';

    protected $casts = [
        'capa' => 'boolean',
        'contracapa' => 'boolean',
        'apendice' => 'boolean',
        'referencias' => 'boolean',
    ];

    protected $fillable = [
        'nome',
        'texto',
        'versao',
        'status',
        'contracapa',
        'capa',
        'arquivo_capa',
        'apendice',
        'referencias',
    ];

    public static function latestVersion($modeloFormularioId)
    {
        $registro = self::find($modeloFormularioId);

        return $registro ? $registro->versao : 1;
    }

    public function modelosAnalisesFormularios()
    {
        return $this->hasMany(\App\Models\Sapc\ModeloAnaliseFormulario::class, 'modelo_formulario_id');
    }

    public function analisesFormularios()
    {
        return $this->hasMany(AnaliseFormulario::class, 'modelo_formulario_id');
    }

    protected static function boot()
    {
        parent::boot();

        static::creating(static function ($modeloFormulario) {
            $modeloFormulario->versao = 1;
        });

        static::updating(static function ($modeloFormulario) {
            $modeloFormulario->versao = static::latestVersion($modeloFormulario->id) + 1;
            $parsedTexto = $modeloFormulario->texto;

            $pattern = '/==#(.*?)#/';

            if (preg_match_all($pattern, $modeloFormulario->texto, $matches)) {
                foreach ($matches[1] as $key => $value) {
                    $newValue = strip_tags($value);
                    $parsedTexto = str_replace($value, $newValue, $parsedTexto);
                }
            }

            $modeloFormulario->texto = $parsedTexto;
        });
    }
}
