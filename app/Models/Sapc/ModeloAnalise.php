<?php

namespace App\Models\Sapc;

use App\Models\Diretoria;
use App\Models\TipoUnidade;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class ModeloAnalise extends Model
{
    use HasFactory;

    protected $table = 'sapc.modelo_analise';

    protected $fillable = [
        'nome', 'data_inicio', 'data_fim', 'tipo_analise', 'esfera', 'diretoria_id', 'cabecalho', 'imagem_cabecalho', 'rodape', 'imagem_rodape',
    ];

    protected $casts = [
        'data_inicio' => 'datetime',
        'data_fim' => 'datetime',
    ];

    /**
     * Get the analise.
     */
    public function diretoria()
    {
        return $this->belongsTo(Diretoria::class);
    }

    public function modelosAnalisesFormularios()
    {
        return $this->hasMany(\App\Models\Sapc\ModeloAnaliseFormulario::class, 'modelo_analise_id');
    }

    public function formularios()
    {
        return $this->belongsToMany(ModeloFormulario::class, 'sapc.modelo_analise_formulario')->using(ModeloAnaliseFormulario::class);
    }

    public function analises()
    {
        return $this->hasMany(\App\Models\Sapc\Analise::class, 'modelo_analise_id');
    }

    public function tiposUnidades()
    {
        return $this->belongsToMany(TipoUnidade::class, 'sapc.modelo_analise_tipo_unidade_gestora', 'modelo_analise_id', 'tipo_ug_id');
    }
}
