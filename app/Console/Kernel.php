<?php

namespace App\Console;

use App\Jobs\ProcessInconsistencia;
use App\Jobs\UpdateOverdueMessagesStatus;
use App\Lib\Cardug\Client;
use GuzzleHttp\Exception\ConnectException;
use GuzzleHttp\Exception\RequestException;
use Illuminate\Console\Scheduling\Schedule;
use Illuminate\Foundation\Console\Kernel as ConsoleKernel;
use Illuminate\Support\Facades\Log;

class Kernel extends ConsoleKernel
{
    /**
     * The Artisan commands provided by your application.
     *
     * @var array
     */
    protected $commands = [
        //
    ];

    /**
     * Define the application's command schedule.
     *
     * @return void
     */
    protected function schedule(Schedule $schedule)
    {
        $schedule->call(function () {
            try {
                $url = config('cardug.url');
                $login = config('cardug.login');
                $password = config('cardug.password');

                $client = new Client($url, $login, $password);

                $client->syncUnidadesGestoras();
                $client->syncUpdateUnidadesGestoras();
                $client->syncUsers();
                $client->syncPermissoesRemessas();
            } catch (ConnectException $e) {
                Log::error('Guzzle HTTP Connect Error: '.$e);
                Log::error('Failed URL: '.$e->getRequest()->getUri());
            } catch (RequestException $e) {
                Log::error('Guzzle HTTP Client Error: '.$e);
                if ($e->hasResponse()) {
                    $response = $e->getResponse();
                    Log::error('Guzzle HTTP Client Response: '.$response->getBody());
                }
            } catch (\Throwable $th) {
                Log::error('Falha na sincronização do Cardug: '.$th);
            }
        })->dailyAt('00:00');

        $schedule->job(new ProcessInconsistencia)->hourly();
        $schedule->job(new UpdateOverdueMessagesStatus)->dailyAt('02:00');

        $schedule->command('hard-delete:remessas')->dailyAt('04:00');
        $schedule->command('refresh:dados-consolidados')->hourly();

    }

    /**
     * Register the commands for the application.
     *
     * @return void
     */
    protected function commands()
    {
        $this->load(__DIR__.'/Commands');

        require base_path('routes/console.php');
    }
}
