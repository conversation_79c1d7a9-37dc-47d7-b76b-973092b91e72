import { computed, watchEffect } from "vue";
import { useRoute } from "vue-router";
import { useMenuStore } from "@/stores/auditor/menuStore";

import MainMenuAuditorConfig from "@/config/MainMenuAuditorConfig";
import MainMenuJurisdicionadoConfig from "@/config/MainMenuJurisdicionadoConfig";

export function useMainMenuConfig() {
  const route = useRoute();
  const analiseId = route.params.analiseId;
  const menuStore = useMenuStore();

  const isPortalAuditor = computed(() => {
    return location.pathname.includes("/portal-auditor");
  });

  const isSapc = computed(() => {
    return route.path.includes("/e-contas/analise/");
  });

  watchEffect(async () => {
    if (isSapc.value) {
      menuStore.fetchSapcMenu(analiseId as string);
      return;
    }

    if (isPortalAuditor.value) {
      menuStore.setMainMenuItens(MainMenuAuditorConfig);
      return;
    }

    menuStore.setMainMenuItens(MainMenuJurisdicionadoConfig);
  });
}
