<template>
  <div class="ai-flex ai-justify-between ai-gap-6">
    <AiStatCard
      variant="info"
      elevation="0"
      :value="stats.openedMessages"
      caption="Mensagens não lidas"
      :icon="IconOpenedMessages"
    />

    <AiStatCard
      variant="warning"
      elevation="0"
      :value="stats.inProgressMessages"
      caption="Mensagens em andamento"
      :icon="IconInProgressMessages"
    />

    <AiStatCard
      variant="danger"
      elevation="0"
      :value="stats.overdueMessages"
      caption="Mensagens atrasadas"
      :icon="IconDelayedMessage"
    />
  </div>
</template>

<script setup lang="ts">
  import { useFetchSummary } from "@/composables/mensagens/useFetchSummary";
  import { useFilterResquestData } from "@/composables/mensagens/useFilterResquestData";

  import IconOpenedMessages from "~icons/solar/chat-line-outline";
  import IconInProgressMessages from "~icons/solar/dialog-2-outline";
  import IconDelayedMessage from "~icons/solar/chat-unread-outline";
  import { ref, watch } from "vue";

  const { filterRequestData } = useFilterResquestData();

  const defaultStats = {
    openedMessages: "-",
    inProgressMessages: "-",
    overdueMessages: "-"
  };

  const stats = ref(defaultStats);
  const timer = ref<number | null>(null);

  const fetchSummary = async () => {
    const { data, error } = await useFetchSummary(
      filterRequestData.value,
      defaultStats
    );

    stats.value = data.value;

    if (error.value) {
      console.error(error.value);
    }
  };

  watch(filterRequestData, () => {
    clearTimeout(timer.value);

    timer.value = setTimeout(() => {
      fetchSummary();
    }, 1000);
  });

  await fetchSummary();
</script>
