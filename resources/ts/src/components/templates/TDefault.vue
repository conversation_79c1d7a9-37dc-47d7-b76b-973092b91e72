<template>
  <div
    class="ai-relative ai-inset-0 ai-min-h-screen ai-bg-surface-container-hi"
  >
    <AiDrawer>
      <AiDrawerHeader>
        <AiDrawerHeaderBranding>
          <img
            v-if="isHomolog"
            alt="Logo"
            :src="getAssetPath('media/logos/logo-dark-homolog.png')"
          />
          <img
            v-else
            alt="Logo"
            :src="getAssetPath('media/logos/logo-dark.png')"
          />
        </AiDrawerHeaderBranding>
      </AiDrawerHeader>

      <div
        ref="scrollElRef"
        class="ai-h-full ai-overflow-y-auto"
        style="
          scrollbar-width: thin;
          scrollbar-color: hsl(var(--ai-color-surface-container-hi))
            hsl(var(--ai-color-surface-container-hi));
        "
      >
        <div
          class="ai-flex ai-h-full ai-flex-col ai-gap-y-4 ai-overflow-hidden"
        >
          <CMainMenuSapcAnaliseDetails class="ai-ml-4" />

          <div
            ref="scrollElRef"
            class="ai-h-full ai-overflow-y-auto"
            style="
              scrollbar-width: thin;
              scrollbar-color: hsl(var(--ai-color-surface-container-hi))
                hsl(var(--ai-color-surface-container-hi));
            "
          >
            <AiMenu :items="mainMenuItens" />
          </div>
        </div>
      </div>
    </AiDrawer>

    <AiTopAppBar>
      <AiTopAppBarTitle
        :page-title="pageTitle"
        :breadcrumbs="breadcrumbs"
        :breadcrumbs-link="breadcrumbsLink"
      />

      <AiTopAppBarMessage>
        <span
          v-if="isHomolog"
          class="ai-rounded-full ai-bg-danger ai-px-4 ai-py-2 ai-text-white"
        >
          Ambiente de Homologação - Dados sem validade
        </span>
      </AiTopAppBarMessage>

      <AiTopAppBarActions>
        <AiTopAppBarActionsGroup>
          <CAppNotifications />
        </AiTopAppBarActionsGroup>

        <div
          class="ai-ml-4 ai-mr-6 ai-h-11 ai-w-px ai-bg-on-surface-variant/20"
        />

        <AiTopAppBarActionsGroup>
          <AiUserMenu @logout="logout" :user="loggedUser">
            <AiDropdownMenuItem @click="seeAllNotifications">
              <IconSolarBellOutline />
              Ver todas as notificações
            </AiDropdownMenuItem>
          </AiUserMenu>
        </AiTopAppBarActionsGroup>
      </AiTopAppBarActions>
    </AiTopAppBar>

    <AiPane>
      <AiPaneContent>
        <HomologMsg />

        <router-view />
      </AiPaneContent>

      <KTFooter />
    </AiPane>

    <CModalDownload />
  </div>

  <KTScrollTop />

  <v-progress-linear
    v-if="fetchState === 'fetching'"
    color="deep-purple"
    height="2"
    indeterminate
    absolute
    style="z-index: 100000"
  />
</template>

<script setup lang="ts">
  import { computed, onMounted, ref } from "vue";
  import { getAssetPath } from "@/core-metronic-jurisdicionado/helpers/assets";
  import { useFetchStore } from "@/stores/global/fetchStore";
  import { useAuthStore } from "@/stores/jurisdicionado/authStore";
  import { useAuthStore as useAuthStoreAuditor } from "@/stores/auditor/authStore";
  import { useAuth } from "@websanova/vue-auth/src/v3.js";
  import { useAuthUser } from "@/composables/auth/useAuthUser";
  import { useMenuStore } from "@/stores/auditor/menuStore";
  import { useRoute, useRouter } from "vue-router";

  import KTFooter from "@/layouts-metronic-jurisdicionado/main-layout/footer/Footer.vue";
  import KTScrollTop from "@/layouts-metronic-jurisdicionado/main-layout/extras/ScrollTop.vue";
  import HomologMsg from "@/components/HomologMsg.vue";

  import { useMainMenuConfig } from "@/composables/global/UseMainMenuConfig";

  const { user } = useAuthUser();
  const fetchStore = useFetchStore();
  const authStore = useAuthStore();
  const authStoreAuditor = useAuthStoreAuditor();
  const auth = useAuth();
  const menuStore = useMenuStore();
  const route = useRoute();
  const router = useRouter();

  const loggedUser = computed(() => {
    return {
      name: user.value?.name,
      role: user.value?.role
    };
  });

  const pageTitle = computed(() => {
    return (route?.meta?.pageTitle as string) || "SIAP";
  });

  const breadcrumbs = computed(() => {
    return route?.meta?.breadcrumbs as string[];
  });

  const breadcrumbsLink = computed(() => {
    const bcLinks = route.meta.breadcrumbsLink as {
      title: string;
      link: string;
    }[];

    if (!bcLinks) return [];

    return bcLinks.map((link) => {
      if (link && link.link && link.link.includes(":")) {
        const dynamicParams = link.link.match(/:([^/]+)/g);
        if (dynamicParams) {
          dynamicParams.forEach((param) => {
            const paramName = param.slice(1);
            if (route.params[paramName]) {
              link.link = link.link.replace(
                param,
                route.params[paramName] as string
              );
            }
          });
        }
      }
      return link;
    });
  });

  const mainMenuItens = computed(() => menuStore.mainMenuItens);

  const fetchState = computed(() => fetchStore.fetchState);

  const isHomolog = computed(() => {
    const APP_ENVIRONMENT =
      import.meta.env.VITE_APP_ENVIRONMENT === undefined
        ? "prod"
        : import.meta.env.VITE_APP_ENVIRONMENT;

    return APP_ENVIRONMENT === "homolog";
  });

  const isPortalAuditor = computed(() => {
    return location.pathname.includes("/portal-auditor");
  });

  const scrollElRef = ref<null | HTMLElement>(null);

  const logout = async () => {
    if (isPortalAuditor.value) {
      authStoreAuditor.logout();
      location.href = "/admin/logout";
    } else {
      authStore.logout();
      await auth.logout({
        makeRequest: true,
        redirect: "/login"
      });
    }
  };

  const seeAllNotifications = () => {
    router.push({ name: "notificacoes" });
  };

  onMounted(() => {
    if (scrollElRef.value) {
      scrollElRef.value.scrollTop = 0;
    }
  });

  const metronicStyle = document.getElementById("metronic");
  metronicStyle && metronicStyle.remove();

  useMainMenuConfig();
</script>
