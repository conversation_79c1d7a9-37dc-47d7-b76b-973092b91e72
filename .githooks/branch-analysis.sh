#!/bin/bash

# Cores para melhorar a legibilidade no terminal
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

MAIN_BRANCH="dev"

echo -e "${BLUE}🚀 Iniciando análise de diferenças com a branch $MAIN_BRANCH...${NC}"

# Verifica se a branch principal existe localmente
if ! git show-ref --verify --quiet refs/heads/$MAIN_BRANCH; then
    echo -e "${RED}❌ Branch $MAIN_BRANCH não encontrada localmente.${NC}"
    echo -e "${YELLOW}⚠️ Tentando buscar do remoto...${NC}"
    git fetch origin $MAIN_BRANCH:$MAIN_BRANCH || {
        echo -e "${RED}❌ Falha ao buscar a branch $MAIN_BRANCH. Verifique sua configuração.${NC}"
        exit 1
    }
fi

# Verifica arquivos PHP modificados em relação à branch principal
PHP_FILES=$(git diff --name-only $MAIN_BRANCH... --diff-filter=ACM | grep -E '\.php$')
# DIFF_FILES=$(git diff dev...HEAD --name-only --diff-filter=ACM | grep -E '\.php$')

if [ -z "$PHP_FILES" ]; then
    echo -e "${YELLOW}⚠️ Nenhum arquivo PHP modificado em relação à branch $MAIN_BRANCH.${NC}"
    exit 0
fi

echo -e "${YELLOW}📝 Arquivos PHP modificados em relação à $MAIN_BRANCH:${NC}"
echo "$PHP_FILES"

# Executa PHP Insights para correção automática
echo -e "${YELLOW}💡 Executando PHP Insights...${NC}"
./vendor/bin/sail artisan insights  $PHP_FILES --no-interaction --fix
./vendor/bin/sail artisan insights  $PHP_FILES --no-ansi --format=json | grep -Po '\{.*\}' > public/build/insights_branch.json

# Executa Pint em todos os arquivos modificados
echo -e "${YELLOW}🧹 Executando Laravel Pint...${NC}"
./vendor/bin/sail pint $PHP_FILES

# Executa PHPStan
echo -e "${YELLOW}🕵️ Executando PHPStan...${NC}"
# ./vendor/bin/sail bin phpstan analyse $PHP_FILES --memory-limit=2G
./vendor/bin/sail bin phpstan analyse $PHP_FILES --error-format=prettyJson --no-progress | sed -n '/^{/,/^}/p' > public/build/phpstan_branch.json
PHPSTAN_EXIT_CODE=$?

if [ $PHPSTAN_EXIT_CODE -ne 0 ]; then
    echo -e "${RED}❌ PHPStan encontrou problemas!${NC}"
    exit 1
fi

echo -e "${GREEN}✅ Análise concluída! O código está em conformidade com as verificações.${NC}"