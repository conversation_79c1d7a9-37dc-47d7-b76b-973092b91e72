<?php

namespace Tests\Feature\App\Route;

use App\Enums\Comportamento;
use App\Enums\Esfera;
use App\Enums\GrupoRegional;
use App\Enums\SentStatusQuestionario;
use App\Enums\TipoAdministracao;
use App\Models\Cidade;
use App\Models\Diretoria;
use App\Models\PeriodoRemessa;
use App\Models\Questionario;
use App\Models\Sicap\Exercicio2022\TabelasAuxiliares\PoderOrgao;
use App\Models\SubtipoUnidade;
use App\Models\TipoLayout;
use App\Models\TipoQuestionario;
use App\Models\TipoUnidade;
use App\Models\UnidadeGestora;
use App\Traits\TestTrait;
use Illuminate\Database\Eloquent\Factories\Sequence;
use Illuminate\Foundation\Testing\DatabaseTransactions;
use Illuminate\Support\Facades\Config;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Str;
use Tests\TestCase;

class ApiTest extends TestCase
{
    use DatabaseTransactions, TestTrait;

    public function test_api_middleware_without_headers()
    {
        $response = $this->getJson('/api/unidades-gestoras');
        $response->assertUnauthorized();
    }

    public function test_api_middleware_with_headers()
    {
        $apiTokenFallback = '12345678';

        Config::set('app.env', 'local');
        Config::set('auth.api_token_fallback', $apiTokenFallback);

        $response = $this->withHeaders([
            'User-Agent' => 'PostmanRuntime/*',
            'X-API-Token' => $apiTokenFallback,
        ])->getJson('/api/unidades-gestoras');

        $response->assertOk();
    }

    public function test_fetch_exercicios_com_remessas()
    {
        $this->createUserAuthenticationSessionWith(UnidadeGestora::factory()->create());

        PeriodoRemessa::factory()
            ->count(4)
            ->state(new Sequence(
                ['exercicio' => 2021],
                ['exercicio' => 2022],
                ['exercicio' => 2023],
                ['exercicio' => 2024],
                ['exercicio' => 2024],
            ))
            ->create();

        $response = $this->getJson('/api/exercicios');

        $response->assertOk();
        $response->assertJson([
            'data' => [
                [
                    'text' => 2021,
                    'value' => 2021,
                ],
                [
                    'text' => 2022,
                    'value' => 2022,
                ],
                [
                    'text' => 2023,
                    'value' => 2023,
                ],
                [
                    'text' => 2024,
                    'value' => 2024,
                ],
            ],
        ]);
    }

    public function test_fetch_exercicios_sem_remessas()
    {
        $this->createUserAuthenticationSessionWith(UnidadeGestora::factory()->create());

        $response = $this->getJson('/api/exercicios');

        $response->assertOk();
        $response->assertJson([
            'data' => [],
        ]);
    }

    public function test_fetch_all_unidades_gestoras()
    {
        $ug = UnidadeGestora::factory()->create();
        $this->createUserAuthenticationSessionWith(null);
        $response = $this->get('/api/unidades-gestoras/admin');
        $response->assertOk();
        $response->assertJsonFragment(['nome' => $ug->nome]);
    }

    public function test_fetch_unidades_gestoras()
    {
        $ugDesvinculada = UnidadeGestora::factory()
            ->create(['nome' => 'UG Desvinculada']);
        $ug = UnidadeGestora::factory()->create();
        $this->createUserAuthenticationSessionWith($ug);
        $response = $this->get('/api/unidades-gestoras');
        $response->assertOk();
        $response->assertJsonFragment(['nome' => $ug->nome]);
        $response->assertJsonMissing(['nome' => $ugDesvinculada->nome]);
    }

    public function test_fetch_tipos_unidade()
    {
        DB::statement(
            "SELECT setval(pg_get_serial_sequence('tipo_unidades', 'id'), coalesce(max(id), 1)) FROM tipo_unidades;"
        );

        $tiposUnidade = collect([
            ['description' => 'Tipo 1'],
            ['description' => 'Tipo 2'],
            ['description' => 'Tipo 3'],
        ])->map(fn ($data) => TipoUnidade::create($data));

        [$tipoUnidade1, $tipoUnidade2, $tipoUnidade3] = $tiposUnidade;

        $this->createUserAuthenticationSessionWith(UnidadeGestora::factory()->create());

        $response = $this->getJson('/api/tipos-unidade');
        $response->assertOk()
            ->assertJsonFragment(['value' => $tipoUnidade1->id, 'text' => $tipoUnidade1->description])
            ->assertJsonFragment(['value' => $tipoUnidade2->id, 'text' => $tipoUnidade2->description])
            ->assertJsonFragment(['value' => $tipoUnidade3->id, 'text' => $tipoUnidade3->description]);
    }

    public function test_fetch_municipios()
    {
        [$cidade1, $cidade2] = Cidade::factory()
            ->count(2)
            ->sequence(
                ['id' => 999998],
                ['id' => 999999],
            )
            ->create();

        $this->createUserAuthenticationSessionWith(UnidadeGestora::factory()->create());

        $response = $this->getJson('/api/municipios');
        $response->assertOk();

        $response->assertJsonFragment(['value' => $cidade1->id, 'text' => $cidade1->nome]);
        $response->assertJsonFragment(['value' => $cidade2->id, 'text' => $cidade2->nome]);
    }

    public function test_fetch_municipios_by_nome()
    {
        [$cidade1, $cidade2, $cidade3] = Cidade::factory()
            ->count(3)
            ->sequence(
                ['id' => 999997, 'nome' => 'AaAbbb'],
                ['id' => 999998, 'nome' => 'BbbcccAAA'],
                ['id' => 999999, 'nome' => 'CCCddd'],
            )
            ->create();

        $this->createUserAuthenticationSessionWith(UnidadeGestora::factory()->create());

        $response = $this->getJson('/api/municipios?nome=aaa');
        $response->assertOk();

        $response->assertJsonFragment(['value' => $cidade1->id, 'text' => $cidade1->nome]);
        $response->assertJsonFragment(['value' => $cidade2->id, 'text' => $cidade2->nome]);
        $response->assertJsonMissing(['value' => $cidade3->id, 'text' => $cidade3->nome]);
        $response->assertJsonCount(2, 'data');
    }

    public function test_fech_municipios_with_filters_and_invalid_values()
    {
        Cidade::factory()
            ->count(3)
            ->sequence(
                ['id' => 999997, 'nome' => 'AaAbbb', 'uf' => 'SP'],
                ['id' => 999998, 'nome' => 'BbbcccAAA', 'uf' => 'RJ'],
                ['id' => 999999, 'nome' => 'CCCddd', 'uf' => 'SP'],
            )
            ->create();

        $this->createUserAuthenticationSessionWith(UnidadeGestora::factory()->create());

        $response = $this->getJson('/api/municipios?nome=a&uf=s');
        $response->assertUnprocessable();
        $response->assertJsonValidationErrors('nome')
            ->assertJsonFragment(['O campo nome deve ter no mínimo 3 caracteres.']);

        $filtroNome = Str::random(256);
        $response = $this->getJson("/api/municipios?nome={$filtroNome}");
        $response->assertUnprocessable();
        $response->assertJsonValidationErrors('nome')
            ->assertJsonFragment(['O campo nome deve ter no máximo 255 caracteres.']);
    }

    public function test_fetch_relatorias()
    {
        $this->createUserAuthenticationSessionWith(UnidadeGestora::factory()->create());

        $response = $this->getJson('/api/relatorias');
        $response->assertOk();

        $relatorias = GrupoRegional::asSelectArray();

        foreach ($relatorias as $key => $value) {
            $response->assertJsonFragment([
                'value' => $key,
                'text' => $value,
            ]);
        }
    }

    public function test_fetch_esferas()
    {
        $this->createUserAuthenticationSessionWith(UnidadeGestora::factory()->create());

        $response = $this->getJson('/api/esferas');
        $response->assertOk();

        $esferas = Esfera::asSelectArray();

        foreach ($esferas as $key => $value) {
            $response->assertJsonFragment([
                'value' => $key,
                'text' => $value,
            ]);
        }
    }

    public function test_fetch_poderes()
    {
        [$poder1, $poder2, $poder3] = collect([
            ['codigo' => '99997', 'descricao' => 'Poder 1'],
            ['codigo' => '99998', 'descricao' => 'Poder 2'],
            ['codigo' => '99999', 'descricao' => 'Poder 3'],
        ])->map(fn ($poder) => PoderOrgao::create($poder));

        $this->createUserAuthenticationSessionWith(UnidadeGestora::factory()->create());

        $response = $this->getJson('/api/poderes');
        $response->assertOk();

        $response->assertJsonFragment(['value' => $poder1->codigo, 'text' => $poder1->descricao]);
        $response->assertJsonFragment(['value' => $poder2->codigo, 'text' => $poder2->descricao]);
        $response->assertJsonFragment(['value' => $poder3->codigo, 'text' => $poder3->descricao]);
    }

    public function test_fetch_questionario_status()
    {
        $this->createUserAuthenticationSessionWith(UnidadeGestora::factory()->create());

        $response = $this->getJson('/api/questionario/status');
        $response->assertOk();

        $statuses = SentStatusQuestionario::asSelectArray();

        foreach ($statuses as $key => $value) {
            $response->assertJsonFragment([
                'value' => $key,
                'text' => $value,
            ]);
        }
    }

    public function test_fetch_questionario_tipos()
    {
        [$tipoQuestionario1, $tipoQuestionario2] = TipoQuestionario::factory()->count(2)->create();

        $this->createUserAuthenticationSessionWith(UnidadeGestora::factory()->create());

        $response = $this->getJson('/api/questionario/tipos');
        $response->assertOk();

        $response->assertJsonFragment([
            'value' => $tipoQuestionario1->id,
            'text' => $tipoQuestionario1->sigla,
        ]);

        $response->assertJsonFragment([
            'value' => $tipoQuestionario2->id,
            'text' => $tipoQuestionario2->sigla,
        ]);

        // Garante que os tipos estão ordenados por sigla
        $responseData = $response->json();
        $siglas = array_column($responseData, 'text');
        $siglasOrdenadas = $siglas;
        sort($siglasOrdenadas);

        $this->assertEquals($siglasOrdenadas, $siglas, 'Os tipos de questionário devem estar ordenados por sigla');
    }

    public function testfetch_grupos_regionais()
    {
        $this->createUserAuthenticationSessionWith(UnidadeGestora::factory()->create());

        $response = $this->getJson('/api/grupos-regionais');
        $response->assertOk();

        $grupos = GrupoRegional::asSelectArray();

        foreach ($grupos as $key => $value) {
            $response->assertJsonFragment([
                'value' => $key,
                'text' => $value,
            ]);
        }
    }

    public function test_fetch_tipos_administracao()
    {
        $this->createUserAuthenticationSessionWith(UnidadeGestora::factory()->create());

        $response = $this->getJson('/api/tipos-administracao');
        $response->assertOk();

        $tiposAdministracao = TipoAdministracao::asSelectArray();

        foreach ($tiposAdministracao as $key => $value) {
            $response->assertJsonFragment([
                'value' => $key,
                'text' => $value,
            ]);
        }
    }

    public function test_fetch_subtipos_unidades()
    {
        DB::statement(
            "SELECT setval(pg_get_serial_sequence('subtipo_unidades', 'id'), coalesce(max(id), 1)) FROM subtipo_unidades;"
        );

        [$subtipoUnidade1, $subtipoUnidade2] = SubtipoUnidade::factory()->count(2)->create();

        $this->createUserAuthenticationSessionWith(UnidadeGestora::factory()->create());

        $response = $this->getJson('/api/subtipos-unidades');
        $response->assertOk();

        $response->assertJsonFragment([
            'value' => $subtipoUnidade1->id,
            'text' => $subtipoUnidade1->descricao,
        ]);
        $response->assertJsonFragment([
            'value' => $subtipoUnidade2->id,
            'text' => $subtipoUnidade2->descricao,
        ]);
    }

    public function test_fetch_diretorias()
    {
        [$diretoria1, $diretoria2] = Diretoria::factory()->count(2)->create();

        $this->createUserAuthenticationSessionWith(UnidadeGestora::factory()->create());

        $response = $this->getJson('/api/diretorias');
        $response->assertOk();

        $response->assertJsonFragment([
            'value' => $diretoria1->id,
            'text' => $diretoria1->sigla,
        ])->assertJsonFragment([
            'value' => $diretoria2->id,
            'text' => $diretoria2->sigla,
        ]);
    }

    public function test_fetch_comportamentos()
    {
        $this->createUserAuthenticationSessionWith(UnidadeGestora::factory()->create());

        $response = $this->getJson('/api/comportamentos');
        $response->assertOk();

        $comportamentos = Comportamento::asSelectArray();

        foreach ($comportamentos as $key => $value) {
            $response->assertJsonFragment([
                'value' => $key,
                'text' => $value,
            ]);
        }
    }

    public function testfetch_periodos_remessa()
    {
        $arrayTeste = ['nome' => 'teste'];
        PeriodoRemessa::factory(3)->create($arrayTeste);
        $this->createUserAuthenticationSessionWith(null);
        $response = $this->get('/api/periodos-remessa?'.http_build_query($arrayTeste));
        $response->assertOk();
        $response->assertJsonCount(3, 'data');
    }

    public function test_fetch_tipos_layouts()
    {
        $arrayTeste = ['nome' => 'teste'];
        $tipoLayout = TipoLayout::factory()->create($arrayTeste);
        $this->createUserAuthenticationSessionWith(null);

        $response = $this->getJson('/api/tipos-layouts?'.http_build_query($arrayTeste));
        $response->assertOk()
            ->assertJsonCount(1, 'data')
            ->assertJsonFragment(['nome' => $tipoLayout->nome]);
    }

    public function test_fetch_naturezas_lancamento()
    {
        $arrayTeste = ['nome' => 'patrimo'];
        $this->createUserAuthenticationSessionWith(null);
        $response = $this->get('api/naturezas-lancamento?'.http_build_query($arrayTeste));
        $response->assertOk()
            ->assertJsonCount(1, 'data')
            ->assertJsonFragment([
                'text' => 'Patrimonial',
                'value' => 'patrimonial',
            ]);
    }

    public function test_fetch_periodo_remessa_status()
    {
        $this->createUserAuthenticationSessionWith(null);

        $response = $this->getJson('/api/periodo-remessa/status');
        $response->assertOk()
            ->assertJsonCount(2, 'data')
            ->assertExactJson([
                'data' => [
                    [
                        'value' => 'aberta',
                        'text' => 'Aberta',
                    ],
                    [
                        'value' => 'fechada',
                        'text' => 'Fechada',
                    ],
                ],
            ]);
    }

    public function test_fetch_questionario_exercicios()
    {
        $questionario = Questionario::first();
        $periodoQuestionario = $questionario->periodoQuestionarios()->create([
            'data_inicio' => now(),
            'data_fim' => now(),
            'exercicio' => '2024',
            'nome' => 'Teste',
        ]);
        $this->createUserAuthenticationSessionWith(null);

        $response = $this->getJson('/api/questionario/exercicios');
        $response->assertOk()
            ->assertJsonCount(1, 'data')
            ->assertJsonFragment(['text' => $periodoQuestionario->exercicio]);
    }
}
