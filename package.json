{"name": "auditoria", "private": true, "type": "module", "scripts": {"dev": "vite", "build": "run-p type-check build-only", "prod": "run-p type-check build-only", "preview": "vite preview --port 8084", "build-only": "vite build --base=/build/ && npm run copy-assets", "copy-assets": "cp -rfu public/appfonts public/build/appfonts && cp -rfu public/media public/build/media && cp -rfu public/fonts public/build/fonts", "type-check": "vue-tsc --declaration --emitDeclarationOnly", "lint-metronic": "eslint . --ext .vue,.js,.jsx,.cjs,.mjs,.ts,.tsx,.cts,.mts --fix --ignore-path .gitignore resources/ts", "lint": "eslint --ext .js,.vue,.jsx,.cjs,.mjs,.ts,.tsx,.cts,.mts --ignore-path .gitignore --fix-dry-run resources/ts", "format": "prettier ./resources/ts --write", "nova:install": "npm --prefix='../../vendor/laravel/nova' ci", "storybook": "storybook dev -p 6006", "build-storybook": "storybook build", "build:tokens": "cd resources/ts/src/assets/design-system && node build.mjs", "stats:treemap": "vite-bundle-visualizer -o public/build/stats/stats-treemap.html"}, "dependencies": {"@bradoctech/ai-ui": "^0.5.17", "@ckeditor/ckeditor5-alignment": "40.2.0", "@ckeditor/ckeditor5-autoformat": "40.2.0", "@ckeditor/ckeditor5-autosave": "40.2.0", "@ckeditor/ckeditor5-basic-styles": "40.2.0", "@ckeditor/ckeditor5-block-quote": "40.2.0", "@ckeditor/ckeditor5-cloud-services": "40.2.0", "@ckeditor/ckeditor5-editor-decoupled": "40.2.0", "@ckeditor/ckeditor5-essentials": "40.2.0", "@ckeditor/ckeditor5-find-and-replace": "40.2.0", "@ckeditor/ckeditor5-font": "40.2.0", "@ckeditor/ckeditor5-heading": "40.2.0", "@ckeditor/ckeditor5-highlight": "40.2.0", "@ckeditor/ckeditor5-horizontal-line": "40.2.0", "@ckeditor/ckeditor5-html-support": "40.2.0", "@ckeditor/ckeditor5-image": "40.2.0", "@ckeditor/ckeditor5-indent": "40.2.0", "@ckeditor/ckeditor5-language": "40.2.0", "@ckeditor/ckeditor5-link": "40.2.0", "@ckeditor/ckeditor5-list": "40.2.0", "@ckeditor/ckeditor5-mention": "40.2.0", "@ckeditor/ckeditor5-page-break": "40.2.0", "@ckeditor/ckeditor5-paragraph": "40.2.0", "@ckeditor/ckeditor5-paste-from-office": "40.2.0", "@ckeditor/ckeditor5-remove-format": "40.2.0", "@ckeditor/ckeditor5-restricted-editing": "40.2.0", "@ckeditor/ckeditor5-select-all": "40.2.0", "@ckeditor/ckeditor5-source-editing": "40.2.0", "@ckeditor/ckeditor5-special-characters": "40.2.0", "@ckeditor/ckeditor5-style": "40.2.0", "@ckeditor/ckeditor5-table": "40.2.0", "@ckeditor/ckeditor5-theme-lark": "^40.2.0", "@ckeditor/ckeditor5-typing": "40.2.0", "@ckeditor/ckeditor5-undo": "40.2.0", "@ckeditor/ckeditor5-upload": "40.2.0", "@ckeditor/ckeditor5-vue": "^5.1.0", "@ckeditor/ckeditor5-watchdog": "40.2.0", "@ckeditor/ckeditor5-word-count": "40.2.0", "@fullcalendar/core": "^6.1.8", "@fullcalendar/daygrid": "^6.1.8", "@fullcalendar/interaction": "^6.1.8", "@fullcalendar/list": "^6.1.8", "@fullcalendar/timegrid": "^6.1.8", "@fullcalendar/vue3": "^6.1.8", "@mdi/font": "^7.4.47", "@popperjs/core": "^2.11.8", "@tanstack/vue-table": "^8.20.5", "@vue/compat": "^3.2.36", "@vue/compiler-sfc": "^3.3.4", "@vueform/multiselect": "^2.6.6", "@vuelidate/validators": "^2.0.4", "@websanova/vue-auth": "^4.2.1", "animate.css": "^4.1.1", "apexcharts": "^4.1.0", "axios": "^1.9.0", "bootstrap": "^5.3.3", "class-variance-authority": "^0.7.0", "clipboard": "^2.0.8", "clsx": "^2.1.1", "deepmerge": "^4.2.2", "dropzone": "^5.9.3", "esbuild-wasm": "^0.24.0", "fontfaceobserver": "^2.1.0", "mini-toastr": "^0.8.1", "moment": "^2.29.4", "nouislider": "^15.7.1", "npm-run-all": "^4.1.5", "object-path": "^0.11.8", "perfect-scrollbar": "^1.5.0", "pinia": "^2.0.23", "pinia-shared-state": "^0.5.1", "portal-vue": "^3.0.0", "puppeteer": "^22.4.1", "quill": "^1.3.6", "radix-vue": "^1.9.7", "sweetalert2": "^11.7.31", "tailwind-merge": "^2.6.0", "tailwindcss-animate": "^1.0.7", "vee-validate": "^4.12.6", "vue": "^3.2.41", "vue-axios": "^3.5.2", "vue-diff": "^1.2.4", "vue-i18n": "^9.14.4", "vue-multiselect": "^2.1.8", "vue-router": "^4.2.4", "vue-the-mask": "^0.11.1", "vue3-apexcharts": "^1.4.1", "vue3-treeview": "^0.4.1", "vuelidate": "^0.7.7", "vuetify": "3.5.10", "vuetify-use-dialog": "^0.6.6", "web-pki": "^2.14.6", "yup": "^1.2.0"}, "devDependencies": {"@chromatic-com/storybook": "^1.2.25", "@ckeditor/ckeditor5-core": "^40.2.0", "@ckeditor/ckeditor5-ui": "^40.2.0", "@ckeditor/vite-plugin-ckeditor5": "^0.1.3", "@fontsource/roboto": "^5.2.5", "@iconify-json/mdi": "^1.1.67", "@iconify-json/solar": "^1.1.9", "@rushstack/eslint-patch": "^1.7.2", "@storybook/addon-essentials": "^8.0.5", "@storybook/addon-interactions": "^8.0.5", "@storybook/addon-links": "^8.0.5", "@storybook/blocks": "^8.0.5", "@storybook/test": "^8.0.5", "@storybook/vue3": "^8.0.5", "@storybook/vue3-vite": "^8.6.14", "@tsconfig/node18": "^18.2.2", "@types/array-sort": "^1.0.0", "@types/bootstrap": "^5.2.6", "@types/node": "^20.14.12", "@types/object-path": "^0.11.0", "@types/prismjs": "^1.26.0", "@types/quill": "^2.0.9", "@typescript-eslint/eslint-plugin": "^7.1.1", "@typescript-eslint/parser": "^7.1.1", "@vitejs/plugin-vue": "^4.2.3", "@vue/tsconfig": "^0.1.3", "autoprefixer": "^10.4.19", "cross-env": "^7.0.3", "eslint": "^8.57.1", "eslint-config-prettier": "^9.1.0", "eslint-plugin-prettier": "^5.1.3", "eslint-plugin-storybook": "^0.8.0", "eslint-plugin-vue": "^9.23.0", "jquery": "^3.6.0", "laravel-echo": "^1.16.1", "laravel-mix": "^6.0.6", "laravel-vite-plugin": "^1.0.0", "lodash": "^4.17.21", "msw": "^2.4.9", "msw-storybook-addon": "^2.0.3", "postcss-loader": "^8.1.1", "postcss-nested": "^6.2.0", "prettier": "^3.3.3", "prettier-plugin-tailwindcss": "^0.6.5", "pusher-js": "^8.4.0-rc2", "resolve-url-loader": "^5.0.0", "rollup-plugin-bundle-stats": "^4.13.3", "sass": "^1.81.0", "storybook": "^8.6.14", "storybook-vue3-router": "^5.0.0", "style-dictionary": "^3.9.0", "tailwindcss": "^3.4.7", "typescript": "5.6.3", "unplugin-icons": "^0.19.0", "unplugin-vue-components": "^0.27.2", "vite": "^5.0.0", "vite-bundle-visualizer": "^1.2.1", "vite-plugin-dynamic-import": "^1.5.0", "vite-plugin-eslint": "^1.8.1", "vue-cli-plugin-vuetify": "^2.5.8", "vue-loader": "^17.0.0", "vue-tsc": "^2.1.10"}, "browserslist": ["> 1%", "last 2 versions", "not ie <= 10"], "msw": {"workerDirectory": ["public"]}}