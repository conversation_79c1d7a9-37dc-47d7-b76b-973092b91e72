#Este arquivo e usado pelo ambiente de teste para criação dos containers do sistema.

APP_NAME=Laravel
APP_ENV=local

APP_KEY=base64:+89WCa1ZHtPAfL2XEJlknHRMr0sHZ8A/2YCdQD1dJVQ=

APP_DEBUG=true
APP_URL=http://localhost

BROADCAST_DRIVER=log
CACHE_DRIVER=file
QUEUE_CONNECTION=sync
SESSION_DRIVER=file
SESSION_LIFETIME=120

CERTIFICATE_PATH=certificate
CERTIFICATE_NAME="Tribunal de Contas do Estado de Alagoas"
CERTIFICATE_LOCATION=www.tceal.tc.br
CERTIFICATE_REASON=""
CERTIFICATE_CONTACT=https://www.tceal.tc.br/view/portal-transparencia/diario-oficial.php

DB_CONNECTION=pgsql
DB_HOST=postgres-auditoria
DB_PORT=5432
DB_DATABASE=auditoria_testing
DB_USERNAME=postgres
DB_PASSWORD=postgres
DB_SCHEMAS=comunicacao,crawler,relatorio,sapc,tabelas_auxiliares,exercicio_2021,exercicio_2022,exercicio_2024,exercicio_2025
DB_TRUNCATE_ONLY=true

ID_CONTEXTO_SEGURANCA_LACUNA=12a0b5db-1ee7-43e5-a7c3-75d7e056d563

LDAP_LOGGING=true
LDAP_CONNECTION=default
LDAP_HOST=127.0.0.1
LDAP_USERNAME="cn=user,dc=local,dc=com"
LDAP_PASSWORD=secret
LDAP_PORT=389
LDAP_BASE_DN="dc=local,dc=com"
LDAP_TIMEOUT=5
LDAP_SSL=false
LDAP_TLS=false

LOG_CHANNEL=stack
LOG_LEVEL=debug

MAIL_MAILER=smtp
MAIL_HOST=smtp.mailtrap.io
MAIL_PORT=2525
MAIL_USERNAME=null
MAIL_PASSWORD=null
MAIL_ENCRYPTION=null
MAIL_FROM_ADDRESS=null
MAIL_FROM_NAME="${APP_NAME}"

VITE_BASE_URL=""
VITE_CLIENT_NAME="Tribunal de Contas de Alagoas"
VITE_PUSHER_APP_KEY="${PUSHER_APP_KEY}"
VITE_PUSHER_APP_CLUSTER="${PUSHER_APP_CLUSTER}"

# Metronic vars
VITE_APP_NAME="SIAP"
VITE_APP_VERSION="8.2.0"
VITE_APP_DEMO="demo8"
VITE_APP_FULL_NAME="SIAP - Sistema Integrado de Auditoria Pública"
VITE_APP_API_URL="/api"

MIX_BASE_URL="/"
MIX_PUSHER_APP_KEY="${PUSHER_APP_KEY}"
MIX_PUSHER_APP_CLUSTER="${PUSHER_APP_CLUSTER}"

PUSHER_APP_ID=
PUSHER_APP_KEY=
PUSHER_APP_SECRET=
PUSHER_APP_CLUSTER=mt1

# Configuração do Lacuna
# Token válido ate o dia 30/09/2022
# Arquivo que contém chave no repositório: https://github.com/LacunaSoftware/PkiSuiteSamples/blob/master/php/plain/config.php
LACUNA_ENDPOINT=https://pki.rest/
LACUNA_ACCESS_TOKEN="Yuy9-PZcP_qPggH00h9oOJzbvDnoyctHg4sq_DEjUGeyTLUtC8uls4RpJiVGdndhpo0BEsrYSGG9bshOe5Zbxfi5GX0YEazEyLA8svwHPiQehruwOKc5YsxBX8-8kgcmOpv0W1oYuevG2_UEVTUO1h9f9bHPPnJJJxhTU4mNo_9u8MZLv4ZhjAszX4qUYZvs-xZ6OdBRULil73NNpNZ0DSwwzkw7ZYb8GGKZUnJgbU70YpguKQSTHkI18zSO8JFA9wN2oGVlVJormZu5ATaBxb8YgJlMrLwJMgM1bXCvIoRRO_QWSdibcaCAL0nDnBa3nrtvCQGbl8yXZzERJwP746Xe2RlOubcHpBobBwfP8Mhfm2lQwStxwr8Xepoy_K5w1XsvuHPBuCWjrJiCrD2Fw48wZ_WuqWBnNE0Z29-bNUWVn-hNL3Be0OcGgDvrX-s7YGL5Q9ThDd_xHquWYrQYMy5J6FTztvsSck9HOB460vyr5IpKawIzOtbRS-ABQAdiYnaxlw"
LACUNA_WEBPKI_LICENSE=null
LACUNA_TEST=true

# configuracao do Cardug
CARDUG_URL="http://localhost:8000/api"

SCOUT_DRIVER=null

DISABLE_LAZYLOADING=false
